import React, { useEffect, useState, useRef } from 'react';
import { router, usePage } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import CommentsSection from '@/Pages/Comments/CommentsSection';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Category, Notification, Tag } from '@/types';
import UpvoteButton from '@/Components/VoteButton';
import { AvatarWithFallback } from '@/Components/ui/avatar-with-fallback';
import { ArrowLeft, Clock, User, Building, AlertTriangle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface TicketDetailProps {
  ticket: {
    id: string;
    title: string;
    content: string;
    created_at: string;
    updated_at: string;
    user: any;
    categories: Category[];
    tags: Tag[];
    comments: any[];
    upvote_count: number;
    has_upvote: boolean;
    priority: string;
    priority_name: string;
    status: string;
    status_name: string;
    assignee?: any;
    department?: any;
    is_auto_assigned: boolean;
    automation_history: any[];
  };
  categories: Category[];
  tags: Tag[];
  departments: any[];
  users: any[];
}

const TicketDetail: React.FC<TicketDetailProps> = ({
  ticket,
  categories,
  tags,
  departments,
  users,
}) => {
  const { props } = usePage();
  const { notifications } = props as any;
  const [comments, setComments] = useState(ticket.comments || []);
  const [currentUser, setCurrentUser] = useState(null);
  const commentsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Get current user from props or auth
    setCurrentUser((props as any).auth?.user || null);
  }, [props]);

  const handleCommentSubmit = async (content: string) => {
    try {
      const response = await fetch(`/posts/${ticket.id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN':
            document
              .querySelector('meta[name="csrf-token"]')
              ?.getAttribute('content') || '',
        },
        body: JSON.stringify({ content }),
      });

      if (response.ok) {
        const newComment = await response.json();
        setComments(prev => [...prev, newComment]);

        // Scroll to comments section
        if (commentsRef.current) {
          commentsRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in_progress':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'closed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const title = `${ticket.title} - Support Ticket`;

  return (
    <AppLayout
      title={title}
      canLogin={true}
      canRegister={true}
      notifications={notifications}
    >
      <div className="max-w-[1354px] mx-auto px-4">
        <div className="flex">
          <div className="flex-1 w-full max-w-4xl mx-auto mt-4 sm:mt-5 md:mt-7 px-4 sm:px-6 md:px-4">
            {/* Back Button */}
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={() => router.get('/tickets')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Tickets
              </Button>
            </div>

            {/* Ticket Header */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6 mb-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                    {ticket.title}
                  </h1>

                  <div className="flex flex-wrap items-center gap-3 mb-4">
                    <Badge className={getPriorityColor(ticket.priority)}>
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {ticket.priority_name || ticket.priority}
                    </Badge>
                    <Badge className={getStatusColor(ticket.status)}>
                      {ticket.status_name || ticket.status}
                    </Badge>
                    {ticket.department && (
                      <Badge variant="outline">
                        <Building className="h-3 w-3 mr-1" />
                        {ticket.department.name}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <UpvoteButton
                    postId={ticket.id}
                    upvoteCount={ticket.upvote_count}
                    hasUpvote={ticket.has_upvote}
                  />
                </div>
              </div>

              {/* Ticket Meta Info */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <div className="flex items-center gap-2">
                  <AvatarWithFallback
                    src={ticket.user?.profile_photo_path}
                    alt={ticket.user?.name}
                    fallback={ticket.user?.name}
                    className="h-6 w-6"
                  />
                  <span>Created by {ticket.user?.name}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />

                  {ticket.created_at}
                </div>

                {ticket.assignee && (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>Assigned to</span>
                    <AvatarWithFallback
                      src={ticket.assignee?.profile_photo_path}
                      alt={ticket.assignee?.name}
                      name={ticket.assignee?.name || 'Unknown'}
                      className="h-6 w-6"
                    />
                    <span>{ticket.assignee?.name}</span>
                  </div>
                )}
              </div>

              {/* Categories and Tags */}
              {(ticket.categories.length > 0 || ticket.tags.length > 0) && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {ticket.categories.map(category => (
                    <Link
                      key={category.id}
                      href={`/categories/${(category.slug)}/posts`}

                    >
                      <Badge
                        variant="outline"
                        className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200
                                                 rounded border border-gray-200 border-dashed dark:border-gray-600 hover:border-blue-600
                                                  dark:hover:border-blue-400 dark:bg-[#0F1014]"
                      >
                        {category.title}
                      </Badge>
                    </Link>
                  ))}
                  {ticket.tags.map(tag => (
                    <Badge key={tag.id} variant="outline" className="text-xs">
                      #{tag.name}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Ticket Content */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6 mb-6">
              <div
                className="prose dark:prose-invert max-w-none"
                dangerouslySetInnerHTML={{ __html: ticket.content }}
              />
            </div>

            {/* Automation History */}
            {ticket.automation_history &&
              ticket.automation_history.length > 0 && (
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-4 mb-6">
                  <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Automation History
                  </h3>
                  <div className="space-y-2">
                    {ticket.automation_history.map((history, index) => (
                      <div
                        key={index}
                        className="text-sm text-blue-800 dark:text-blue-200"
                      >
                        {history.message}
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Comments Section */}
            <div ref={commentsRef}>
              <CommentsSection
                comments={comments}
                currentUser={currentUser}
                onCommentSubmit={handleCommentSubmit}
              />
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default TicketDetail;
