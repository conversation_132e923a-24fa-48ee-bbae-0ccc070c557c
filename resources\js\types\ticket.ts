export interface Comment {
  id: number
  content: string
  created_at: string
  user: {
    id: number
    name: string
    email: string
    profile?: string
    profile_photo_path?: string
    roles?: string[]
    departments?: string[]
  }
  is_hr_response?: boolean
  parent_id?: number
  replies?: Comment[]
}

export interface Ticket {
  upvotedBy: any
  upvotes: ReactNode
  createdAt(createdAt: any, arg1: { addSuffix: true }): import("react").ReactNode
  replies: any
  description: ReactNode
  category: any
  author: any
  id: number
  slug: string
  title: string
  content: string
  status: string
  priority: string
  created_at: string
  updated_at: string
  profile?: string
  user: {
    id: number
    name: string
    email: string
    profile_photo_path?: string
  }
  assignee?: {
    avatar: string | undefined
    isStaff: any
    id: number
    name: string
    email: string
    profile_photo_path?: string
  }
  department?: {
    id: number
    name: string
  }
  categories?: Array<{
    id: number
    title: string
  }>
  tags?: Array<{
    id: number
    name: string
  }>
  upvote_count?: number
  comments: Comment[]
}